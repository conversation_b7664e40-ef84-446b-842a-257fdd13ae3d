import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { fetchSellerRequests } from "../../redux/slices/sellerDashboardSlice";
import SellerLayout from "../../components/seller/SellerLayout";
import Table from "../../components/common/Table";
import LoadingSkeleton from "../../components/common/LoadingSkeleton";
import { ErrorDisplay } from "../../components/common/ErrorBoundary";
import RequestResponseModal from "../../components/seller/RequestResponseModal";
import "../../styles/SellerRequestsStandardized.css";
import { FiEye, FiCheck, FiX, FiClock, FiDollarSign } from "react-icons/fi";
import { MdRequestPage } from "react-icons/md";
import { toast } from "react-toastify";
import api from "../../services/api";
import { formatStandardDate } from "../../utils/dateValidation";

const SellerRequests = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  // Redux state
  const { requests, loading, errors } = useSelector((state) => state.sellerDashboard);

  // Local state
  const [selectedRequest, setSelectedRequest] = useState(null);
  const [isResponseModalOpen, setIsResponseModalOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [filter, setFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  // Fetch requests on component mount
  useEffect(() => {
    dispatch(fetchSellerRequests());
  }, [dispatch]);

  // Ensure requests is always an array
  const requestsArray = Array.isArray(requests) ? requests : [];

  // Filter requests based on status and search term
  const filteredRequests = requestsArray.filter(request => {
    const matchesFilter = filter === 'all' || request.status?.toLowerCase() === filter.toLowerCase();
    const matchesSearch = !searchTerm ||
      request.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.buyer?.firstName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.buyer?.lastName?.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesFilter && matchesSearch;
  });

  const handleViewDetails = (request) => {
    navigate(`/seller/request-details/${request._id}`);
  };

  const handleRespond = (request) => {
    setSelectedRequest(request);
    setIsResponseModalOpen(true);
  };

  const handleResponseSubmit = async (responseData) => {
    setIsSubmitting(true);
    try {
      await api.put(`/requests/${selectedRequest._id}/respond`, responseData);
      toast.success(`Request ${responseData.accepted ? 'accepted' : 'rejected'} successfully!`);
      setIsResponseModalOpen(false);
      setSelectedRequest(null);
      // Refresh requests
      dispatch(fetchSellerRequests());
    } catch (error) {
      console.error('Error responding to request:', error);
      toast.error(error.response?.data?.message || 'Failed to respond to request');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRequestRemainingPayment = async (requestId) => {
    try {
      await api.put(`/requests/${requestId}/request-remaining-payment`);
      toast.success('Remaining payment requested successfully!');
      dispatch(fetchSellerRequests());
    } catch (error) {
      console.error('Error requesting remaining payment:', error);
      toast.error(error.response?.data?.message || 'Failed to request remaining payment');
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      'Pending': { color: 'orange', icon: <FiClock /> },
      'Accepted': { color: 'green', icon: <FiCheck /> },
      'Rejected': { color: 'red', icon: <FiX /> },
      'In Progress': { color: 'blue', icon: <FiClock /> },
      'Content Submitted': { color: 'purple', icon: <FiCheck /> },
      'Completed': { color: 'green', icon: <FiCheck /> }
    };

    const config = statusConfig[status] || { color: 'gray', icon: <FiClock /> };

    return (
      <span className={`status-badge status-${config.color}`}>
        {config.icon}
        {status}
      </span>
    );
  };

  const columns = [
    { key: "no", label: "No.", className: "no" },
    {
      key: "request",
      label: "Request Details",
      render: (item) => (
        <div className="request-details">
          <div className="request-title">{item.title}</div>
          <div className="request-meta">
            <span className="content-type">{item.contentType}</span>
            <span className="sport">{item.sport}</span>
          </div>
        </div>
      ),
    },
    {
      key: "buyer",
      label: "Buyer",
      render: (item) => (
        <div className="buyer-info">
          <div className="buyer-name">
            {item.buyer?.firstName} {item.buyer?.lastName}
          </div>
          <div className="buyer-email">{item.buyer?.email}</div>
        </div>
      ),
    },
    {
      key: "budget",
      label: "Budget",
      render: (item) => (
        <div className="budget-info">
          <span className="budget-amount">${item.budget}</span>
        </div>
      ),
    },
    {
      key: "date",
      label: "Date",
      render: (item) => formatStandardDate(item.createdAt)
    },
    {
      key: "status",
      label: "Status",
      render: (item) => getStatusBadge(item.status),
    },
    {
      key: "action",
      label: "Actions",
      render: (item) => (
        <div className="action-buttons">
          <button
            className="action-btn view-btn"
            onClick={() => handleViewDetails(item)}
            title="View Details"
          >
            <FiEye />
          </button>
          {item.status === 'Pending' && (
            <button
              className="action-btn respond-btn"
              onClick={() => handleRespond(item)}
              title="Respond to Request"
            >
              <FiCheck />
            </button>
          )}
          {item.status === 'In Progress' &&
           item.sellerResponse?.paymentType === 'half' &&
           item.paymentDetails?.initialPaymentCompleted &&
           !item.remainingPaymentRequested && (
            <button
              className="action-btn payment-btn"
              onClick={() => handleRequestRemainingPayment(item._id)}
              title="Request Remaining Payment"
            >
              <FiDollarSign />
            </button>
          )}
        </div>
      ),
    },
  ];

  const formatData = (requests) => {
    return requests.map((item, index) => ({
      ...item,
      no: index + 1,
    }));
  };

  if (loading.requests) {
    return (
      <SellerLayout>
        <div className="seller-requests-container">
          <LoadingSkeleton count={5} height="60px" />
        </div>
      </SellerLayout>
    );
  }

  if (errors.requests) {
    return (
      <SellerLayout>
        <div className="seller-requests-container">
          <ErrorDisplay
            error={errors.requests}
            onRetry={() => dispatch(fetchSellerRequests())}
            title="Failed to load requests"
          />
        </div>
      </SellerLayout>
    );
  }

  return (
    <SellerLayout>
      <div className="seller-requests-container">
        {/* Header with filters */}
        <div className="requests-header">
          <div className="requests-stats">
            <div className="stat-item">
              <MdRequestPage className="stat-icon" />
              <span className="stat-count">{requestsArray.length}</span>
              <span className="stat-label">Total Requests</span>
            </div>
            <div className="stat-item">
              <FiClock className="stat-icon" />
              <span className="stat-count">
                {requestsArray.filter(r => r.status === 'Pending').length}
              </span>
              <span className="stat-label">Pending</span>
            </div>
            <div className="stat-item">
              <FiCheck className="stat-icon" />
              <span className="stat-count">
                {requestsArray.filter(r => r.status === 'Completed').length}
              </span>
              <span className="stat-label">Completed</span>
            </div>
          </div>

          <div className="requests-filters">
            <div className="filter-group">
              <select
                value={filter}
                onChange={(e) => setFilter(e.target.value)}
                className="filter-select"
              >
                <option value="all">All Status</option>
                <option value="pending">Pending</option>
                <option value="accepted">Accepted</option>
                <option value="in progress">In Progress</option>
                <option value="completed">Completed</option>
                <option value="rejected">Rejected</option>
              </select>
            </div>

            <div className="search-group">
              <input
                type="text"
                placeholder="Search requests..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="search-input"
              />
            </div>
          </div>
        </div>

        {/* Requests table */}
        <div className="requests-table-container">
          {filteredRequests.length === 0 ? (
            <div className="empty-state">
              <MdRequestPage className="empty-icon" />
              <h3>No Custom Requests</h3>
              <p>
                {searchTerm || filter !== 'all'
                  ? 'No requests match your current filters.'
                  : 'You haven\'t received any custom requests yet. Enable custom requests on your strategies to start receiving them.'}
              </p>
            </div>
          ) : (
            <Table
              columns={columns}
              data={formatData(filteredRequests)}
              className="requests-table"
              emptyMessage="No requests found."
            />
          )}
        </div>

        {/* Response Modal */}
        {isResponseModalOpen && selectedRequest && (
          <RequestResponseModal
            isOpen={isResponseModalOpen}
            onClose={() => {
              setIsResponseModalOpen(false);
              setSelectedRequest(null);
            }}
            request={selectedRequest}
            onSubmit={handleResponseSubmit}
            isSubmitting={isSubmitting}
          />
        )}
      </div>
    </SellerLayout>
  );
};

export default SellerRequests;
