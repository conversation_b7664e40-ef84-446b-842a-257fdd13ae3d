/* BuyerRequests Component Styles - Standardized Table Pattern */
/* Scoped to BuyerRequests to avoid global conflicts */

.BuyerRequests {
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* Stats Section */
.BuyerRequests .requests-stats {
  display: flex;
  gap: var(--basefont);
  margin-bottom: var(--heading6);
  flex-wrap: wrap;
}

.BuyerRequests .stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--basefont);
  background: var(--white);
  border-radius: var(--border-radius-large);
  min-width: 100px;
  box-shadow: var(--box-shadow-light);
  border: 1px solid var(--light-gray);
}

.BuyerRequests .stat-icon {
  font-size: var(--heading5);
  margin-bottom: var(--smallfont);
  color: var(--primary-color);
}

.BuyerRequests .stat-count {
  font-size: var(--heading3);
  font-weight: 700;
  color: var(--secondary-color);
  margin-bottom: 4px;
}

.BuyerRequests .stat-label {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
  text-align: center;
  font-weight: 500;
}

/* Filters Section */
.BuyerRequests .requests-filters {
  display: flex;
  gap: var(--basefont);
  align-items: center;
  padding: var(--basefont);
  background: var(--white);
  border-radius: var(--border-radius-large);
  margin-bottom: var(--heading6);
  box-shadow: var(--box-shadow-light);
  border: 1px solid var(--light-gray);
  flex-wrap: wrap;
}

.BuyerRequests .filter-group {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.BuyerRequests .filter-group label {
  font-size: var(--smallfont);
  color: var(--secondary-color);
  font-weight: 500;
}

.BuyerRequests .filter-select,
.BuyerRequests .search-input {
  padding: var(--smallfont) var(--basefont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  background: var(--white);
  color: var(--text-color);
}

.BuyerRequests .search-input {
  min-width: 200px;
}

/* Standardized Table Container */
.BuyerRequests .table-container {
  background: var(--white);
  border-radius: var(--border-radius-large);
  overflow-x: auto;
  box-shadow: var(--box-shadow-light);
  border: 1px solid var(--light-gray);
}

/* Standardized Table Styling - Following AdminCMSPages pattern */
.BuyerRequests .table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--smallfont);
  background-color: var(--white);
  margin: 0;
}

.BuyerRequests .table th {
  padding: var(--basefont);
  text-align: left;
  vertical-align: middle;
  background-color: var(--bg-gray);
  font-weight: 600;
  color: var(--secondary-color);
  font-size: var(--smallfont);
  border-bottom: 1px solid var(--light-gray);
  white-space: nowrap;
}

.BuyerRequests .table td {
  padding: var(--basefont);
  text-align: left;
  vertical-align: middle;
  border-bottom: 1px solid var(--light-gray);
  font-size: var(--smallfont);
  color: var(--text-color);
}

.BuyerRequests .table tbody tr:last-child td {
  border-bottom: none;
}

/* Request Details Cell */
.BuyerRequests .request-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.BuyerRequests .request-title {
  font-weight: 600;
  color: var(--secondary-color);
  font-size: var(--smallfont);
}

.BuyerRequests .request-meta {
  display: flex;
  gap: var(--smallfont);
  align-items: center;
}

.BuyerRequests .content-type,
.BuyerRequests .sport {
  font-size: var(--extrasmallfont);
  padding: 2px var(--smallfont);
  border-radius: var(--border-radius);
  background: var(--bg-gray);
  color: var(--dark-gray);
}

/* Seller Info Cell */
.BuyerRequests .seller-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.BuyerRequests .seller-name {
  font-weight: 500;
  color: var(--secondary-color);
  font-size: var(--smallfont);
}

.BuyerRequests .seller-email {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

/* Budget Cell */
.BuyerRequests .budget-info {
  display: flex;
  align-items: center;
}

.BuyerRequests .budget-amount {
  font-weight: 600;
  color: var(--primary-color);
  font-size: var(--smallfont);
}

/* Date Cell */
.BuyerRequests .date-info {
  font-size: var(--smallfont);
  color: var(--text-color);
}

/* Status Cell */
.BuyerRequests .status-badge {
  display: inline-block;
  padding: 4px var(--smallfont);
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  font-weight: 600;
  text-transform: capitalize;
}

.BuyerRequests .status-badge.pending {
  background-color: #fef3c7;
  color: #92400e;
}

.BuyerRequests .status-badge.in-progress {
  background-color: #dbeafe;
  color: #1e40af;
}

.BuyerRequests .status-badge.completed {
  background-color: #dcfce7;
  color: #166534;
}

.BuyerRequests .status-badge.cancelled {
  background-color: #fee2e2;
  color: #dc2626;
}

/* Action Buttons - Standardized Pattern */
.BuyerRequests .action-buttons {
  display: flex;
  gap: var(--smallfont);
  align-items: center;
  justify-content: center;
}

.BuyerRequests .action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: transparent;
  color: var(--text-color);
}

.BuyerRequests .view-btn {
  background: var(--bg-gray);
  color: var(--secondary-color);
}

.BuyerRequests .payment-btn {
  background: #fef3c7;
  color: #92400e;
}

/* Empty State */
.BuyerRequests .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--heading3);
  text-align: center;
  color: var(--dark-gray);
}

.BuyerRequests .empty-icon {
  font-size: var(--heading2);
  color: var(--light-gray);
  margin-bottom: var(--basefont);
}

.BuyerRequests .empty-state h3 {
  margin-bottom: var(--smallfont);
  color: var(--secondary-color);
  font-size: var(--heading6);
}

.BuyerRequests .empty-state p {
  color: var(--dark-gray);
  font-size: var(--smallfont);
  max-width: 400px;
}

/* Retry Button */
.BuyerRequests .retry-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--smallfont) var(--basefont);
  background: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  cursor: pointer;
  transition: all 0.3s ease;
}

/* Loading Skeleton */
.BuyerRequests .table-skeleton {
  padding: var(--basefont);
}

/* Error Display */
.BuyerRequests .error-display {
  padding: var(--basefont);
  background: #fee2e2;
  color: #dc2626;
  border-radius: var(--border-radius);
  margin-bottom: var(--basefont);
}

/* Responsive Styles */
@media (max-width: 768px) {
  .BuyerRequests .requests-filters {
    flex-direction: column;
    align-items: stretch;
    gap: var(--basefont);
  }

  .BuyerRequests .filter-group {
    justify-content: space-between;
  }

  .BuyerRequests .search-input {
    min-width: auto;
    width: 100%;
  }

  .BuyerRequests .requests-stats {
    justify-content: center;
  }

  .BuyerRequests .table {
    font-size: var(--extrasmallfont);
  }

  .BuyerRequests .table th,
  .BuyerRequests .table td {
    padding: var(--smallfont);
  }

  .BuyerRequests .action-btn {
    width: 28px;
    height: 28px;
    font-size: var(--extrasmallfont);
  }
}

@media (max-width: 480px) {
  .BuyerRequests .table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .BuyerRequests .table {
    min-width: 600px;
  }

  .BuyerRequests .requests-filters {
    padding: var(--smallfont);
  }

  .BuyerRequests .stat-item {
    min-width: 70px;
    padding: var(--smallfont);
  }

  .BuyerRequests .stat-count {
    font-size: var(--heading5);
  }

  .BuyerRequests .empty-state {
    padding: var(--heading5);
  }
}
